{"chart_name": "Purchase Order Analysis", "chart_type": "Report", "creation": "2020-07-20 21:01:02.203880", "custom_options": "{\"type\": \"donut\", \"height\": 300, \"axisOptions\": {\"shortenYAxisNumbers\": 1}}", "docstatus": 0, "doctype": "Dashboard Chart", "dynamic_filters_json": "{\"company\":\"frappe.defaults.get_user_default(\\\"Company\\\")\",\"from_date\":\"frappe.datetime.add_months(frappe.datetime.nowdate(), -1)\",\"to_date\":\"frappe.datetime.nowdate()\"}", "filters_json": "{\"group_by_po\":0}", "idx": 0, "is_public": 1, "is_standard": 1, "modified": "2020-07-22 12:44:35.754973", "modified_by": "Administrator", "module": "Buying", "name": "Purchase Order Analysis", "number_of_groups": 0, "owner": "Administrator", "report_name": "Purchase Order Analysis", "timeseries": 0, "type": "Donut", "use_report_chart": 1, "y_axis": []}