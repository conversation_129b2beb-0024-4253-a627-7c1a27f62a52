{"charts": [], "content": "[{\"id\":\"vikWSkNm6_\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>Shortcuts</b></span>\",\"col\":12}},{\"id\":\"G984SgVRJN\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Sales Invoice\",\"col\":3}},{\"id\":\"5yHldR0JNk\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"POS Invoice\",\"col\":3}},{\"id\":\"F9f4I1viNr\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Payment Entry\",\"col\":3}},{\"id\":\"1ArNvt9qhz\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Journal Entry\",\"col\":3}},{\"id\":\"4IBBOIxfqW\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Accounts Receivable\",\"col\":3}},{\"id\":\"ILlIxJuexy\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Cost Center\",\"col\":3}},{\"id\":\"B7-uxs8tkU\",\"type\":\"spacer\",\"data\":{\"col\":12}},{\"id\":\"tHb3yxthkR\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>Reports &amp; Masters</b></span>\",\"col\":12}},{\"id\":\"jLgv00c6ek\",\"type\":\"card\",\"data\":{\"card_name\":\"Invoicing\",\"col\":4}},{\"id\":\"npwfXlz0u1\",\"type\":\"card\",\"data\":{\"card_name\":\"Payments\",\"col\":4}},{\"id\":\"am70C27Jrb\",\"type\":\"card\",\"data\":{\"card_name\":\"Dunning\",\"col\":4}},{\"id\":\"xOHTyD8b5l\",\"type\":\"card\",\"data\":{\"card_name\":\"Reports\",\"col\":4}}]", "creation": "2024-01-05 15:29:21.084241", "custom_blocks": [], "docstatus": 0, "doctype": "Workspace", "for_user": "", "hide_custom": 0, "icon": "arrow-right", "idx": 0, "indicator_color": "", "is_hidden": 0, "label": "Receivables", "links": [{"hidden": 0, "is_query_report": 0, "label": "Invoicing", "link_count": 2, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Sales Invoice", "link_count": 0, "link_to": "Sales Invoice", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Customer", "link_count": 0, "link_to": "Customer", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Payments", "link_count": 4, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Payment Entry", "link_count": 0, "link_to": "Payment Entry", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Payment Request", "link_count": 0, "link_to": "Payment Request", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Payment Reconciliation", "link_count": 0, "link_to": "Payment Reconciliation", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Payment Gateway Account", "link_count": 0, "link_to": "Payment Gateway Account", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON>", "link_count": 2, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON>", "link_count": 0, "link_to": "<PERSON><PERSON>", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Dunning Type", "link_count": 0, "link_to": "Dunning Type", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Reports", "link_count": 6, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"dependencies": "Sales Invoice", "hidden": 0, "is_query_report": 1, "label": "Accounts Receivable", "link_count": 0, "link_to": "Accounts Receivable", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Sales Invoice", "hidden": 0, "is_query_report": 1, "label": "Accounts Receivable Summary", "link_count": 0, "link_to": "Accounts Receivable Summary", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Sales Invoice", "hidden": 0, "is_query_report": 1, "label": "Sales Register", "link_count": 0, "link_to": "Sales Register", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Sales Invoice", "hidden": 0, "is_query_report": 1, "label": "Item-wise Sales Register", "link_count": 0, "link_to": "Item-wise Sales Register", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Sales Invoice", "hidden": 0, "is_query_report": 1, "label": "Sales Order Analysis", "link_count": 0, "link_to": "Sales Order Analysis", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Sales Invoice", "hidden": 0, "is_query_report": 1, "label": "Delivered Items To Be Billed", "link_count": 0, "link_to": "Delivered Items To Be Billed", "link_type": "Report", "onboard": 0, "type": "Link"}], "modified": "2024-01-18 22:11:51.474477", "modified_by": "Administrator", "module": "Accounts", "name": "Receivables", "number_cards": [], "owner": "Administrator", "parent_page": "Accounting", "public": 1, "quick_lists": [], "restrict_to_domain": "", "roles": [], "sequence_id": 4.0, "shortcuts": [{"color": "Grey", "doc_view": "List", "label": "POS Invoice", "link_to": "POS Invoice", "stats_filter": "[]", "type": "DocType"}, {"color": "Grey", "doc_view": "List", "label": "Cost Center", "link_to": "Cost Center", "type": "DocType"}, {"doc_view": "", "label": "Sales Invoice", "link_to": "Sales Invoice", "stats_filter": "[]", "type": "DocType"}, {"doc_view": "", "label": "Journal Entry", "link_to": "Journal Entry", "type": "DocType"}, {"doc_view": "", "label": "Payment Entry", "link_to": "Payment Entry", "type": "DocType"}, {"doc_view": "", "label": "Accounts Receivable", "link_to": "Accounts Receivable", "type": "Report"}], "title": "Receivables"}