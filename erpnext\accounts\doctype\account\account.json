{"actions": [], "allow_copy": 1, "allow_import": 1, "creation": "2013-01-30 12:49:46", "description": "Heads (or groups) against which Accounting Entries are made and balances are maintained.", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["properties", "column_break0", "disabled", "account_name", "account_number", "is_group", "company", "root_type", "report_type", "account_currency", "column_break1", "parent_account", "account_type", "tax_rate", "freeze_account", "balance_must_be", "lft", "rgt", "old_parent", "include_in_gross"], "fields": [{"fieldname": "properties", "fieldtype": "Section Break", "oldfieldtype": "Section Break"}, {"fieldname": "column_break0", "fieldtype": "Column Break", "width": "50%"}, {"fieldname": "account_name", "fieldtype": "Data", "in_list_view": 1, "label": "Account Name", "no_copy": 1, "oldfieldname": "account_name", "oldfieldtype": "Data", "reqd": 1}, {"fieldname": "account_number", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Account Number"}, {"default": "0", "fieldname": "is_group", "fieldtype": "Check", "label": "Is Group"}, {"fieldname": "company", "fieldtype": "Link", "in_standard_filter": 1, "label": "Company", "oldfieldname": "company", "oldfieldtype": "Link", "options": "Company", "remember_last_selected_value": 1, "reqd": 1}, {"fieldname": "root_type", "fieldtype": "Select", "in_standard_filter": 1, "label": "Root Type", "options": "\nAsset\nLiability\nIncome\nExpense\nEquity", "read_only": 1}, {"fieldname": "report_type", "fieldtype": "Select", "in_standard_filter": 1, "label": "Report Type", "options": "\nBalance Sheet\nProfit and Loss", "read_only": 1}, {"depends_on": "eval:doc.is_group==0", "fieldname": "account_currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "column_break1", "fieldtype": "Column Break", "width": "50%"}, {"fieldname": "parent_account", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Parent Account", "oldfieldname": "parent_account", "oldfieldtype": "Link", "options": "Account", "reqd": 1, "search_index": 1}, {"description": "Setting Account Type helps in selecting this Account in transactions.", "fieldname": "account_type", "fieldtype": "Select", "in_standard_filter": 1, "label": "Account Type", "oldfieldname": "account_type", "oldfieldtype": "Select", "options": "\nAccumulated Depreciation\nAsset Received But Not Billed\nBank\nCash\nChargeable\nCapital Work in Progress\nCost of Goods Sold\nCurrent Asset\nCurrent Liability\nDepreciation\nDirect Expense\nDirect Income\nEquity\nExpense Account\nExpenses Included In Asset Valuation\nExpenses Included In Valuation\nFixed Asset\nIncome Account\nIndirect Expense\nIndirect Income\nLiability\nPayable\nReceivable\nRound Off\nRound Off for Opening\nStock\nStock Adjustment\nStock Received But Not Billed\nService Received But Not Billed\nTax\nTemporary", "search_index": 1}, {"description": "Rate at which this tax is applied", "fieldname": "tax_rate", "fieldtype": "Float", "label": "Tax Rate", "oldfieldname": "tax_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>"}, {"description": "If the account is frozen, entries are allowed to restricted users.", "fieldname": "freeze_account", "fieldtype": "Select", "label": "Frozen", "oldfieldname": "freeze_account", "oldfieldtype": "Select", "options": "No\nYes"}, {"fieldname": "balance_must_be", "fieldtype": "Select", "label": "Balance must be", "options": "\nDebit\nCredit"}, {"fieldname": "lft", "fieldtype": "Int", "hidden": 1, "label": "Lft", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "rgt", "fieldtype": "Int", "hidden": 1, "label": "Rgt", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "old_parent", "fieldtype": "Data", "hidden": 1, "label": "Old Parent", "print_hide": 1, "read_only": 1}, {"default": "0", "depends_on": "eval:(doc.report_type == 'Profit and Loss' && !doc.is_group)", "fieldname": "include_in_gross", "fieldtype": "Check", "label": "Include in gross"}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disable"}], "icon": "fa fa-money", "idx": 1, "is_tree": 1, "links": [], "modified": "2024-08-19 15:19:11.095045", "modified_by": "Administrator", "module": "Accounts", "name": "Account", "nsm_parent_field": "parent_account", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "write": 1}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Auditor"}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User"}, {"email": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase User"}, {"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}], "search_fields": "account_number", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "ASC", "states": [], "track_changes": 1}