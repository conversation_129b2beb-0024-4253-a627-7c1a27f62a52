{"charts": [{"chart_name": "Profit and Loss", "label": "Profit and Loss"}], "content": "[{\"id\":\"MmUf9abwxg\",\"type\":\"onboarding\",\"data\":{\"onboarding_name\":\"Accounts\",\"col\":12}},{\"id\":\"nDhfcJYbKH\",\"type\":\"chart\",\"data\":{\"chart_name\":\"Profit and Loss\",\"col\":12}},{\"id\":\"VVvJ1lUcfc\",\"type\":\"number_card\",\"data\":{\"number_card_name\":\"Total Outgoing Bills\",\"col\":3}},{\"id\":\"Vlj2FZtlHV\",\"type\":\"number_card\",\"data\":{\"number_card_name\":\"Total Incoming Bills\",\"col\":3}},{\"id\":\"VVVjQVAhPf\",\"type\":\"number_card\",\"data\":{\"number_card_name\":\"Total Incoming Payment\",\"col\":3}},{\"id\":\"DySNdlysIW\",\"type\":\"number_card\",\"data\":{\"number_card_name\":\"Total Outgoing Payment\",\"col\":3}},{\"id\":\"9k1rDm2C0l\",\"type\":\"spacer\",\"data\":{\"col\":12}},{\"id\":\"vikWSkNm6_\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>Shortcuts</b></span>\",\"col\":12}},{\"id\":\"pMywM0nhlj\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Chart of Accounts\",\"col\":3}},{\"id\":\"_pRdD6kqUG\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Sales Invoice\",\"col\":3}},{\"id\":\"G984SgVRJN\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Purchase Invoice\",\"col\":3}},{\"id\":\"1ArNvt9qhz\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Journal Entry\",\"col\":3}},{\"id\":\"F9f4I1viNr\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Payment Entry\",\"col\":3}},{\"id\":\"4IBBOIxfqW\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Accounts Receivable\",\"col\":3}},{\"id\":\"El2anpPaFY\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"General Ledger\",\"col\":3}},{\"id\":\"1nwcM9upJo\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Trial Balance\",\"col\":3}},{\"id\":\"OF9WOi1Ppc\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Dashboard\",\"col\":3}},{\"id\":\"iAwpe-Chra\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Learn Accounting\",\"col\":3}},{\"id\":\"B7-uxs8tkU\",\"type\":\"spacer\",\"data\":{\"col\":12}},{\"id\":\"tHb3yxthkR\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>Reports &amp; Masters</b></span>\",\"col\":12}},{\"id\":\"DnNtsmxpty\",\"type\":\"card\",\"data\":{\"card_name\":\"Accounting Masters\",\"col\":4}},{\"id\":\"nKKr6fjgjb\",\"type\":\"card\",\"data\":{\"card_name\":\"Payments\",\"col\":4}},{\"id\":\"KlqilF5R_V\",\"type\":\"card\",\"data\":{\"card_name\":\"Tax Masters\",\"col\":4}},{\"id\":\"jTUy8LB0uw\",\"type\":\"card\",\"data\":{\"card_name\":\"Cost Center and Budgeting\",\"col\":4}},{\"id\":\"Wn2lhs7WLn\",\"type\":\"card\",\"data\":{\"card_name\":\"Multi Currency\",\"col\":4}},{\"id\":\"PAQMqqNkBM\",\"type\":\"card\",\"data\":{\"card_name\":\"Banking\",\"col\":4}},{\"id\":\"kxhoaiqdLq\",\"type\":\"card\",\"data\":{\"card_name\":\"Opening and Closing\",\"col\":4}},{\"id\":\"q0MAlU2j_Z\",\"type\":\"card\",\"data\":{\"card_name\":\"Subscription Management\",\"col\":4}},{\"id\":\"ptm7T6Hwu-\",\"type\":\"card\",\"data\":{\"card_name\":\"Share Management\",\"col\":4}}]", "creation": "2020-03-02 15:41:59.515192", "custom_blocks": [], "docstatus": 0, "doctype": "Workspace", "for_user": "", "hide_custom": 0, "icon": "accounting", "idx": 0, "indicator_color": "", "is_hidden": 0, "label": "Accounting", "links": [{"hidden": 0, "is_query_report": 0, "label": "Multi Currency", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON><PERSON><PERSON>", "link_count": 0, "link_to": "<PERSON><PERSON><PERSON><PERSON>", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Currency Exchange", "link_count": 0, "link_to": "Currency Exchange", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Exchange Rate Revaluation", "link_count": 0, "link_to": "Exchange Rate Revaluation", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Subscription Management", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Subscription Plan", "link_count": 0, "link_to": "Subscription Plan", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Subscription", "link_count": 0, "link_to": "Subscription", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Subscription Settings", "link_count": 0, "link_to": "Subscription Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Share Management", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Shareholder", "link_count": 0, "link_to": "Shareholder", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Share Transfer", "link_count": 0, "link_to": "Share Transfer", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "Share Transfer", "hidden": 0, "is_query_report": 1, "label": "Share Ledger", "link_count": 0, "link_to": "Share Ledger", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Share Transfer", "hidden": 0, "is_query_report": 1, "label": "Share Balance", "link_count": 0, "link_to": "Share Balance", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Cost Center and Budgeting", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Chart of Cost Centers", "link_count": 0, "link_to": "Cost Center", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Budget", "link_count": 0, "link_to": "Budget", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Accounting Dimension", "link_count": 0, "link_to": "Accounting Dimension", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "Cost Center", "hidden": 0, "is_query_report": 0, "label": "Cost Center Allocation", "link_count": 0, "link_to": "Cost Center Allocation", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "Cost Center", "hidden": 0, "is_query_report": 1, "label": "Budget Variance Report", "link_count": 0, "link_to": "Budget Variance Report", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Monthly Distribution", "link_count": 0, "link_to": "Monthly Distribution", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Opening and Closing", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Opening Invoice Creation Tool", "link_count": 0, "link_to": "Opening Invoice Creation Tool", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Chart of Accounts Importer", "link_count": 0, "link_to": "Chart of Accounts Importer", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Period Closing Voucher", "link_count": 0, "link_to": "Period Closing Voucher", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Banking", "link_count": 6, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Bank", "link_count": 0, "link_to": "Bank", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Bank Account", "link_count": 0, "link_to": "Bank Account", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Bank Clearance", "link_count": 0, "link_to": "Bank Clearance", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Bank Reconciliation Tool", "link_count": 0, "link_to": "Bank Reconciliation Tool", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "GL Entry", "hidden": 0, "is_query_report": 1, "label": "Bank Reconciliation Statement", "link_count": 0, "link_to": "Bank Reconciliation Statement", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Plaid Settings", "link_count": 0, "link_to": "Plaid Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Tax Masters", "link_count": 7, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Sales Taxes and Charges Template", "link_count": 0, "link_to": "Sales Taxes and Charges Template", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Purchase Taxes and Charges Template", "link_count": 0, "link_to": "Purchase Taxes and Charges Template", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Item Tax Template", "link_count": 0, "link_to": "Item Tax Template", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Tax Category", "link_count": 0, "link_to": "Tax Category", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Tax Rule", "link_count": 0, "link_to": "Tax Rule", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Tax Withholding Category", "link_count": 0, "link_to": "Tax Withholding Category", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Lower Deduction Certificate", "link_count": 0, "link_to": "Lower Deduction Certificate", "link_type": "DocType", "onboard": 0, "only_for": "India", "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Accounting Masters", "link_count": 8, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Company", "link_count": 0, "link_to": "Company", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Chart of Accounts", "link_count": 0, "link_to": "Account", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Accounts Set<PERSON>s", "link_count": 0, "link_to": "Accounts Set<PERSON>s", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Fiscal Year", "link_count": 0, "link_to": "Fiscal Year", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Accounting Dimension", "link_count": 0, "link_to": "Accounting Dimension", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Finance Book", "link_count": 0, "link_to": "Finance Book", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Accounting Period", "link_count": 0, "link_to": "Accounting Period", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Payment Term", "link_count": 0, "link_to": "Payment Term", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Payments", "link_count": 5, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Payment Entry", "link_count": 0, "link_to": "Payment Entry", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Journal Entry", "link_count": 0, "link_to": "Journal Entry", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Journal Entry Template", "link_count": 0, "link_to": "Journal Entry Template", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Terms and Conditions", "link_count": 0, "link_to": "Terms and Conditions", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Mode of Payment", "link_count": 0, "link_to": "Mode of Payment", "link_type": "DocType", "onboard": 0, "type": "Link"}], "modified": "2024-01-18 22:15:40.941711", "modified_by": "Administrator", "module": "Accounts", "name": "Accounting", "number_cards": [{"label": "Total Outgoing Bills", "number_card_name": "Total Outgoing Bills"}, {"label": "Total Incoming Bills", "number_card_name": "Total Incoming Bills"}, {"label": "Total Incoming Payment", "number_card_name": "Total Incoming Payment"}, {"label": "Total Outgoing Payment", "number_card_name": "Total Outgoing Payment"}], "owner": "Administrator", "parent_page": "", "public": 1, "quick_lists": [], "restrict_to_domain": "", "roles": [], "sequence_id": 2.0, "shortcuts": [{"color": "Grey", "doc_view": "List", "label": "Learn Accounting", "type": "URL", "url": "https://school.frappe.io/lms/courses/erpnext-accounting?utm_source=in_app"}, {"label": "Chart of Accounts", "link_to": "Account", "type": "DocType"}, {"label": "Sales Invoice", "link_to": "Sales Invoice", "type": "DocType"}, {"label": "Purchase Invoice", "link_to": "Purchase Invoice", "type": "DocType"}, {"label": "Journal Entry", "link_to": "Journal Entry", "type": "DocType"}, {"label": "Payment Entry", "link_to": "Payment Entry", "type": "DocType"}, {"label": "Accounts Receivable", "link_to": "Accounts Receivable", "type": "Report"}, {"label": "General <PERSON><PERSON>", "link_to": "General <PERSON><PERSON>", "type": "Report"}, {"label": "Trial Balance", "link_to": "Trial Balance", "type": "Report"}, {"label": "Dashboard", "link_to": "Accounts", "type": "Dashboard"}], "title": "Accounting"}