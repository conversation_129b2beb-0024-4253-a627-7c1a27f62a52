name: Generate Semantic Release
on:
  push:
    branches:
      - version-15
jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Entire Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          persist-credentials: false

      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: 20

      - name: Setup dependencies
        run: |
          npm install @semantic-release/git @semantic-release/exec --no-save
      - name: Create Release
        env:
          GH_TOKEN: ${{ secrets.RELEASE_TOKEN }}
          GITHUB_TOKEN: ${{ secrets.RELEASE_TOKEN }}
          GIT_AUTHOR_NAME: "Frappe PR Bot"
          GIT_AUTHOR_EMAIL: "<EMAIL>"
          GIT_COMMITTER_NAME: "Frappe PR Bot"
          GIT_COMMITTER_EMAIL: "<EMAIL>"
        run: npx semantic-release
