{"creation": "2021-08-24 12:48:20.763173", "docstatus": 0, "doctype": "Form Tour", "idx": 0, "is_standard": 1, "modified": "2021-08-24 12:48:20.763173", "modified_by": "Administrator", "module": "Assets", "name": "Asset Category", "owner": "Administrator", "reference_doctype": "Asset Category", "save_on_complete": 0, "steps": [{"description": "Name Asset category. You can create categories based on Asset Types like Furniture, Property, Electronics etc.", "field": "", "fieldname": "asset_category_name", "fieldtype": "Data", "has_next_condition": 0, "is_table_field": 0, "label": "Asset Category Name", "parent_field": "", "position": "Bottom", "title": "Asset Category Name"}, {"description": "Check to enable Capital Work in Progress accounting", "field": "", "fieldname": "enable_cwip_accounting", "fieldtype": "Check", "has_next_condition": 0, "is_table_field": 0, "label": "Enable Capital Work in Progress Accounting", "parent_field": "", "position": "Bottom", "title": "Enable CWIP Accounting"}, {"description": "Add a row to define Depreciation Method and other details. Note that you can leave Finance Book blank to have it's accounting done in the primary books of accounts.", "field": "", "fieldname": "finance_books", "fieldtype": "Table", "has_next_condition": 0, "is_table_field": 0, "label": "Finance Books", "parent_field": "", "position": "Bottom", "title": "Finance Book Detail"}, {"description": "Select the Fixed Asset and Depreciation accounts applicable for this Asset Category type", "field": "", "fieldname": "accounts", "fieldtype": "Table", "has_next_condition": 0, "is_table_field": 0, "label": "Accounts", "parent_field": "", "position": "Bottom", "title": "Accounts"}], "title": "Asset Category"}