{"actions": [], "creation": "2023-07-28 12:41:13.232505", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["asset", "column_break_vkdy", "date", "column_break_kkxv", "user", "section_break_romx", "subject"], "fields": [{"fieldname": "asset", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "<PERSON><PERSON>", "options": "<PERSON><PERSON>", "print_width": "165", "read_only": 1, "reqd": 1, "width": "165"}, {"fieldname": "column_break_vkdy", "fieldtype": "Column Break"}, {"fieldname": "section_break_romx", "fieldtype": "Section Break"}, {"fieldname": "subject", "fieldtype": "Small Text", "in_list_view": 1, "label": "Subject", "print_width": "518", "read_only": 1, "reqd": 1, "width": "518"}, {"default": "now", "fieldname": "date", "fieldtype": "Datetime", "in_list_view": 1, "label": "Date", "print_width": "158", "read_only": 1, "reqd": 1, "width": "158"}, {"fieldname": "user", "fieldtype": "Link", "in_list_view": 1, "label": "User", "options": "User", "print_width": "150", "read_only": 1, "reqd": 1, "width": "150"}, {"fieldname": "column_break_kkxv", "fieldtype": "Column Break"}], "in_create": 1, "index_web_pages_for_search": 1, "links": [], "modified": "2023-09-29 15:56:17.608643", "modified_by": "Administrator", "module": "Assets", "name": "Asset Activity", "owner": "Administrator", "permissions": [{"delete": 1, "email": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1}, {"delete": 1, "email": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1}, {"delete": 1, "email": 1, "read": 1, "report": 1, "role": "Quality Manager", "share": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}