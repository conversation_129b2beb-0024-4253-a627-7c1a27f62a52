name: Bug Report
description: Report a bug encountered while using ERPNext
labels: ["bug"]

body:
  - type: markdown
    attributes:
      value: |
        Welcome to ERPNext issue tracker! Before creating an issue, please heed the following:

        1. This tracker should only be used to report bugs and request features / enhancements to ERPNext
            - For questions and general support, checkout the [user manual](https://docs.erpnext.com/) or use [forum](https://discuss.erpnext.com)
            - For documentation issues, propose edit on [documentation site](https://docs.erpnext.com/) directly.
        2. When making a bug report, make sure you provide all required information. The easier it is for
           maintainers to reproduce, the faster it'll be fixed.
        3. If you think you know what the reason for the bug is, share it with us. Maybe put in a PR 😉

  - type: textarea
    id: bug-info
    attributes:
      label: Information about bug
      description: Also tell us, what did you expect to happen?
      placeholder: Please provide as much information as possible.
    validations:
      required: true

  - type: dropdown
    id: module
    attributes:
      label: Module
      description: Select affected module of ERPNext.
      multiple: true
      options:
        - accounts
        - stock
        - buying
        - selling
        - ecommerce
        - manufacturing
        - HR
        - projects
        - support
        - CRM
        - assets
        - integrations
        - quality
        - regional
        - portal
        - agriculture
        - education
        - non-profit
        - other
    validations:
      required: true

  - type: textarea
    id: exact-version
    attributes:
      label: Version
      description: Share exact version number of Frappe and ERPNext you are using.
      placeholder: |
        Frappe version -
        ERPNext Verion -
    validations:
      required: true

  - type: dropdown
    id: install-method
    attributes:
      label: Installation method
      options:
        - docker
        - easy-install
        - manual install
        - FrappeCloud
    validations:
      required: false

  - type: textarea
    id: logs
    attributes:
      label: Relevant log output / Stack trace / Full Error Message.
      description: Please copy and paste any relevant log output. This will be automatically formatted.
      render: shell

  - type: markdown
    attributes:
      value: |
        By submitting this issue, you agree to follow our [Code of Conduct](https://github.com/frappe/erpnext/blob/develop/CODE_OF_CONDUCT.md)
