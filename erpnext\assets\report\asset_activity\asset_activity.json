{"add_total_row": 0, "columns": [], "creation": "2023-08-01 11:14:46.581234", "disabled": 0, "docstatus": 0, "doctype": "Report", "filters": [], "idx": 0, "is_standard": "Yes", "json": "{}", "letterhead": null, "modified": "2023-08-01 11:14:46.581234", "modified_by": "Administrator", "module": "Assets", "name": "Asset Activity", "owner": "Administrator", "prepared_report": 0, "ref_doctype": "Asset Activity", "report_name": "Asset Activity", "report_type": "Report Builder", "roles": [{"role": "System Manager"}, {"role": "Accounts User"}, {"role": "Quality Manager"}]}