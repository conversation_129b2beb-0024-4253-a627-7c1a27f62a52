{"charts": [{"chart_name": "Asset Value Analytics", "label": "Asset Value Analytics"}], "content": "[{\"type\":\"onboarding\",\"data\":{\"onboarding_name\":\"Assets\",\"col\":12}},{\"type\":\"chart\",\"data\":{\"chart_name\":\"Asset Value Analytics\",\"col\":12}},{\"type\":\"spacer\",\"data\":{\"col\":12}},{\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>Your Shortcuts</b></span>\",\"col\":12}},{\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Asset\",\"col\":3}},{\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Asset Category\",\"col\":3}},{\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Fixed Asset Register\",\"col\":3}},{\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Dashboard\",\"col\":3}},{\"type\":\"spacer\",\"data\":{\"col\":12}},{\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>Reports & Masters</b></span>\",\"col\":12}},{\"type\":\"card\",\"data\":{\"card_name\":\"Assets\",\"col\":4}},{\"type\":\"card\",\"data\":{\"card_name\":\"Maintenance\",\"col\":4}},{\"type\":\"card\",\"data\":{\"card_name\":\"Reports\",\"col\":4}}]", "creation": "2020-03-02 15:43:27.634865", "custom_blocks": [], "docstatus": 0, "doctype": "Workspace", "for_user": "", "hide_custom": 0, "icon": "assets", "idx": 0, "is_hidden": 0, "label": "Assets", "links": [{"hidden": 0, "is_query_report": 0, "label": "Assets", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON>", "link_count": 0, "link_to": "<PERSON><PERSON>", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Location", "link_count": 0, "link_to": "Location", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Asset Category", "link_count": 0, "link_to": "Asset Category", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Asset Movement", "link_count": 0, "link_to": "Asset Movement", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Maintenance", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Asset Maintenance Team", "link_count": 0, "link_to": "Asset Maintenance Team", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "Asset Maintenance Team", "hidden": 0, "is_query_report": 0, "label": "Asset Maintenance", "link_count": 0, "link_to": "Asset Maintenance", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "Asset Maintenance", "hidden": 0, "is_query_report": 0, "label": "Asset Maintenance Log", "link_count": 0, "link_to": "Asset Maintenance Log", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 0, "label": "Asset Value Adjustment", "link_count": 0, "link_to": "Asset Value Adjustment", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 0, "label": "As<PERSON> Repair", "link_count": 0, "link_to": "As<PERSON> Repair", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 0, "label": "Asset Capitalization", "link_count": 0, "link_to": "Asset Capitalization", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Reports", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 1, "label": "Asset Depreciation Ledger", "link_count": 0, "link_to": "Asset Depreciation Ledger", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "<PERSON><PERSON>", "hidden": 0, "is_query_report": 1, "label": "Asset Depreciations and Balances", "link_count": 0, "link_to": "Asset Depreciations and Balances", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Asset Maintenance", "hidden": 0, "is_query_report": 0, "label": "Asset Maintenance", "link_count": 0, "link_to": "Asset Maintenance", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Asset Activity", "hidden": 0, "is_query_report": 0, "label": "Asset Activity", "link_count": 0, "link_to": "Asset Activity", "link_type": "Report", "onboard": 0, "type": "Link"}], "modified": "2024-01-05 17:40:34.570041", "modified_by": "Administrator", "module": "Assets", "name": "Assets", "number_cards": [], "owner": "Administrator", "parent_page": "", "public": 1, "quick_lists": [], "restrict_to_domain": "", "roles": [], "sequence_id": 7.0, "shortcuts": [{"label": "<PERSON><PERSON>", "link_to": "<PERSON><PERSON>", "type": "DocType"}, {"label": "Asset Category", "link_to": "Asset Category", "type": "DocType"}, {"label": "Fixed Asset Register", "link_to": "Fixed Asset Register", "type": "Report"}, {"label": "Dashboard", "link_to": "<PERSON><PERSON>", "type": "Dashboard"}], "title": "Assets"}