{"charts": [], "content": "[{\"id\":\"nKKr6fjgjb\",\"type\":\"card\",\"data\":{\"card_name\":\"Ledgers\",\"col\":4}},{\"id\":\"p7NY6MHe2Y\",\"type\":\"card\",\"data\":{\"card_name\":\"Financial Statements\",\"col\":4}},{\"id\":\"3AK1Zf0oew\",\"type\":\"card\",\"data\":{\"card_name\":\"Profitability\",\"col\":4}},{\"id\":\"Q_hBCnSeJY\",\"type\":\"card\",\"data\":{\"card_name\":\"Other Reports\",\"col\":4}}]", "creation": "2024-01-05 16:09:16.766939", "custom_blocks": [], "docstatus": 0, "doctype": "Workspace", "for_user": "", "hide_custom": 0, "icon": "file", "idx": 0, "indicator_color": "", "is_hidden": 0, "label": "Financial Reports", "links": [{"hidden": 0, "is_query_report": 0, "label": "Profitability", "link_count": 0, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"dependencies": "Sales Invoice", "hidden": 0, "is_query_report": 1, "label": "Gross Profit", "link_count": 0, "link_to": "Gross Profit", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "GL Entry", "hidden": 0, "is_query_report": 1, "label": "Profitability Analysis", "link_count": 0, "link_to": "Profitability Analysis", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Sales Invoice", "hidden": 0, "is_query_report": 1, "label": "Sales Invoice Trends", "link_count": 0, "link_to": "Sales Invoice Trends", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Purchase Invoice", "hidden": 0, "is_query_report": 1, "label": "Purchase Invoice Trends", "link_count": 0, "link_to": "Purchase Invoice Trends", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Financial Statements", "link_count": 5, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"dependencies": "GL Entry", "hidden": 0, "is_query_report": 1, "label": "Trial Balance", "link_count": 0, "link_to": "Trial Balance", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "GL Entry", "hidden": 0, "is_query_report": 1, "label": "Profit and Loss Statement", "link_count": 0, "link_to": "Profit and Loss Statement", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "GL Entry", "hidden": 0, "is_query_report": 1, "label": "Balance Sheet", "link_count": 0, "link_to": "Balance Sheet", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "GL Entry", "hidden": 0, "is_query_report": 1, "label": "Cash Flow", "link_count": 0, "link_to": "Cash Flow", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "GL Entry", "hidden": 0, "is_query_report": 1, "label": "Consolidated Financial Statement", "link_count": 0, "link_to": "Consolidated Financial Statement", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Ledgers", "link_count": 3, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"dependencies": "GL Entry", "hidden": 0, "is_query_report": 1, "label": "General <PERSON><PERSON>", "link_count": 0, "link_to": "General <PERSON><PERSON>", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Sales Invoice", "hidden": 0, "is_query_report": 1, "label": "Customer Ledger Summary", "link_count": 0, "link_to": "Customer Ledger Summary", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Sales Invoice", "hidden": 0, "is_query_report": 1, "label": "Supplier Ledger Summary", "link_count": 0, "link_to": "Supplier Ledger Summary", "link_type": "Report", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Other Reports", "link_count": 7, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"dependencies": "GL Entry", "hidden": 0, "is_query_report": 1, "label": "Trial Balance for Party", "link_count": 0, "link_to": "Trial Balance for Party", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Journal Entry", "hidden": 0, "is_query_report": 1, "label": "Payment Period Based On Invoice Date", "link_count": 0, "link_to": "Payment Period Based On Invoice Date", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Sales Invoice", "hidden": 0, "is_query_report": 1, "label": "Sales Partners Commission", "link_count": 0, "link_to": "Sales Partners Commission", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Customer", "hidden": 0, "is_query_report": 1, "label": "Customer Credit Balance", "link_count": 0, "link_to": "Customer Credit Balance", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Sales Invoice", "hidden": 0, "is_query_report": 1, "label": "Sales Payment Summary", "link_count": 0, "link_to": "Sales Payment Summary", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "Address", "hidden": 0, "is_query_report": 1, "label": "Address And Contacts", "link_count": 0, "link_to": "Address And Contacts", "link_type": "Report", "onboard": 0, "type": "Link"}, {"dependencies": "GL Entry", "hidden": 0, "is_query_report": 1, "label": "UAE VAT 201", "link_count": 0, "link_to": "UAE VAT 201", "link_type": "Report", "onboard": 0, "only_for": "United Arab Emirates", "type": "Link"}], "modified": "2024-01-18 22:13:07.596844", "modified_by": "Administrator", "module": "Accounts", "name": "Financial Reports", "number_cards": [], "owner": "Administrator", "parent_page": "Accounting", "public": 1, "quick_lists": [], "restrict_to_domain": "", "roles": [], "sequence_id": 5.0, "shortcuts": [], "title": "Financial Reports"}