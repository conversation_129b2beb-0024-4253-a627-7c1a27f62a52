{"actions": [], "autoname": "field:maintenance_team_name", "creation": "2017-10-20 11:43:47.712616", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["maintenance_team_name", "maintenance_manager", "maintenance_manager_name", "column_break_2", "company", "section_break_2", "maintenance_team_members"], "fields": [{"fieldname": "maintenance_team_name", "fieldtype": "Data", "in_list_view": 1, "label": "Maintenance Team Name", "reqd": 1, "unique": 1}, {"fieldname": "maintenance_manager", "fieldtype": "Link", "label": "Maintenance Manager", "options": "User"}, {"fetch_from": "maintenance_manager.full_name", "fieldname": "maintenance_manager_name", "fieldtype": "Read Only", "label": "Maintenance Manager Name"}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "reqd": 1}, {"fieldname": "section_break_2", "fieldtype": "Section Break", "label": "Team"}, {"fieldname": "maintenance_team_members", "fieldtype": "Table", "label": "Maintenance Team Members", "options": "Maintenance Team Member", "reqd": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2021-01-22 15:09:03.347345", "modified_by": "Administrator", "module": "Assets", "name": "Asset Maintenance Team", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Manufacturing User", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}