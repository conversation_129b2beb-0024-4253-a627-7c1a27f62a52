{"actions": [], "creation": "2018-05-08 14:44:37.095570", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["finance_book", "depreciation_method", "total_number_of_depreciations", "total_number_of_booked_depreciations", "daily_prorata_based", "shift_based", "column_break_5", "frequency_of_depreciation", "depreciation_start_date", "salvage_value_percentage", "expected_value_after_useful_life", "value_after_depreciation", "rate_of_depreciation"], "fields": [{"columns": 2, "fieldname": "finance_book", "fieldtype": "Link", "in_list_view": 1, "label": "Finance Book", "options": "Finance Book"}, {"fieldname": "depreciation_method", "fieldtype": "Select", "in_list_view": 1, "label": "Depreciation Method", "options": "\nStraight Line\nDouble Declining Balance\nWritten Down Value\nManual", "reqd": 1}, {"columns": 2, "fieldname": "total_number_of_depreciations", "fieldtype": "Int", "in_list_view": 1, "label": "Total Number of Depreciations", "reqd": 1}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"columns": 2, "fieldname": "frequency_of_depreciation", "fieldtype": "Int", "in_list_view": 1, "label": "Frequency of Depreciation (Months)", "reqd": 1}, {"fieldname": "depreciation_start_date", "fieldtype": "Date", "in_list_view": 1, "label": "Depreciation Posting Date", "mandatory_depends_on": "eval:parent.doctype == 'Asset'"}, {"columns": 1, "default": "0", "depends_on": "eval:parent.doctype == 'Asset'", "fieldname": "expected_value_after_useful_life", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Expected Value After Useful Life", "options": "Company:company:default_currency"}, {"fieldname": "value_after_depreciation", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Value After Depreciation", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:doc.depreciation_method == 'Written Down Value'", "description": "In Percentage", "fieldname": "rate_of_depreciation", "fieldtype": "Percent", "label": "Rate of Depreciation (%)", "mandatory_depends_on": "eval:doc.depreciation_method == 'Written Down Value'"}, {"fieldname": "salvage_value_percentage", "fieldtype": "Percent", "label": "Salvage Value Percentage"}, {"default": "0", "fieldname": "daily_prorata_based", "fieldtype": "Check", "label": "Depreciate based on daily pro-rata"}, {"default": "0", "depends_on": "eval:doc.depreciation_method == \"Straight Line\"", "fieldname": "shift_based", "fieldtype": "Check", "label": "Depreciate based on shifts"}, {"default": "0", "fieldname": "total_number_of_booked_depreciations", "fieldtype": "Int", "label": "Total Number of Booked Depreciations ", "read_only": 1}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2024-12-13 12:11:03.743209", "modified_by": "Administrator", "module": "Assets", "name": "Asset Finance Book", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}