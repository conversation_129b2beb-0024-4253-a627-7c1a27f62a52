{"actions": [], "autoname": "field:asset_name", "creation": "2017-10-19 16:50:22.879545", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["asset_name", "asset_category", "company", "column_break_3", "item_code", "item_name", "section_break_6", "maintenance_team", "column_break_9", "maintenance_manager", "maintenance_manager_name", "section_break_8", "asset_maintenance_tasks"], "fields": [{"fieldname": "asset_name", "fieldtype": "Link", "in_list_view": 1, "label": "Asset Name", "options": "<PERSON><PERSON>", "reqd": 1, "unique": 1}, {"fetch_from": "asset_name.asset_category", "fieldname": "asset_category", "fieldtype": "Read Only", "label": "Asset Category"}, {"fetch_from": "asset_name.item_code", "fieldname": "item_code", "fieldtype": "Read Only", "label": "Item Code"}, {"fetch_from": "asset_name.item_name", "fieldname": "item_name", "fieldtype": "Read Only", "label": "Item Name"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "reqd": 1}, {"fieldname": "section_break_6", "fieldtype": "Section Break"}, {"fieldname": "maintenance_team", "fieldtype": "Link", "in_list_view": 1, "label": "Maintenance Team", "options": "Asset Maintenance Team", "reqd": 1}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fetch_from": "maintenance_team.maintenance_manager", "fieldname": "maintenance_manager", "fieldtype": "Data", "hidden": 1, "label": "Maintenance Manager", "read_only": 1}, {"fetch_from": "maintenance_team.maintenance_manager_name", "fieldname": "maintenance_manager_name", "fieldtype": "Read Only", "label": "Maintenance Manager Name"}, {"fieldname": "section_break_8", "fieldtype": "Section Break", "label": "Tasks"}, {"fieldname": "asset_maintenance_tasks", "fieldtype": "Table", "label": "Maintenance Tasks", "options": "Asset Maintenance Task", "reqd": 1}], "links": [], "modified": "2020-05-28 20:28:32.993823", "modified_by": "Administrator", "module": "Assets", "name": "Asset Maintenance", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Quality Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Manufacturing User", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}